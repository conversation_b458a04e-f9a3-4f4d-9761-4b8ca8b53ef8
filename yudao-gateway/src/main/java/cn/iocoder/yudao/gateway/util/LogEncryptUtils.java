package cn.iocoder.yudao.gateway.util;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.extra.spring.SpringUtil;
import org.springframework.stereotype.Component;


/**
 * 日志加密工具类，基于 {@link AES} 实现
 * 可通过 mybatis-plus.encryptor.password 配置项，设置密钥
 *
 * <AUTHOR>
 */
@Component
public class LogEncryptUtils {

    private static final String ENCRYPTOR_PROPERTY_NAME = "log.encryptor.password";

    private static AES aes;

    /**
     * 解密字符串
     *
     * @param value 待解密的字符串
     * @return 解密后的字符串
     */
    public static String decrypt(String value) {
        if (StrUtil.isBlank(value)) {
            return value;
        }
        return getEncryptor().decryptStr(value);
    }

    /**
     * 加密字符串
     *
     * @param rawValue 待加密的字符串
     * @return 加密后的字符串
     */
    public static String encrypt(String rawValue) {
        if (StrUtil.isBlank(rawValue)) {
            return rawValue;
        }
        return getEncryptor().encryptBase64(rawValue);
    }

    /**
     * 获取 AES 加密器
     *
     * @return AES 加密器
     */
    private static AES getEncryptor() {
        if (aes != null) {
            return aes;
        }
        // 构建 AES
        String password = SpringUtil.getProperty(ENCRYPTOR_PROPERTY_NAME);
        Assert.notEmpty(password, "配置项({}) 不能为空", ENCRYPTOR_PROPERTY_NAME);
        aes = SecureUtil.aes(password.getBytes());
        return aes;
    }
}
